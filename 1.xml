<definitions 
 xmlns="http://schemas.xmlsoap.org/wsdl/"
 xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" 
 xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" 
 xmlns:s="http://www.w3.org/2001/XMLSchema" 
 xmlns:s0="http://www.dhcc.com.cn" 
 xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" 
 xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" 
 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
 targetNamespace="http://www.dhcc.com.cn">
   <types>
       <s:schema elementFormDefault="qualified" targetNamespace="http://www.dhcc.com.cn">
           <s:element name="HIPMessageServer">
               <s:complexType>
                   <s:sequence>
                       <s:element minOccurs="0" name="action" type="s:string" />
                       <s:element minOccurs="0" name="message" type="s:string" />
                   </s:sequence>
               </s:complexType>
           </s:element>
           <s:element name="HIPMessageServerResponse">
               <s:complexType>
                   <s:sequence>
                       <s:element name="HIPMessageServerResult" type="s:string" />
                   </s:sequence>
               </s:complexType>
           </s:element>
       </s:schema>
   </types>
   <message name="HIPMessageServerSoapIn">
       <part name="parameters" element="s0:HIPMessageServer" />
   </message>
   <message name="HIPMessageServerSoapOut">
       <part name="parameters" element="s0:HIPMessageServerResponse" />
   </message>
   <portType name="ServiceForHATMSoap">
       <operation name="HIPMessageServer">
           <input message="s0:HIPMessageServerSoapIn" />
           <output message="s0:HIPMessageServerSoapOut" />
       </operation>
   </portType>
   <binding name="ServiceForHATMSoap" type="s0:ServiceForHATMSoap">
       <soap:binding transport="http://schemas.xmlsoap.org/soap/http" style="document" />
       <operation name="HIPMessageServer">
           <soap:operation soapAction="http://www.dhcc.com.cn/DHC.Published.ServiceForHATM.BS.ServiceForHATM.HIPMessageServer" style="document" />
           <input>
               <soap:body use="literal" />
           </input>
           <output>
               <soap:body use="literal" />
           </output>
       </operation>
   </binding>
   <service name="ServiceForHATM">
       <port name="ServiceForHATMSoap" binding="s0:ServiceForHATMSoap">
           <soap:address location="https://**********:1443/csp/hsb/DHC.Published.ServiceForHATM.BS.ServiceForHATM.cls" />
       </port>
   </service>
</definitions>